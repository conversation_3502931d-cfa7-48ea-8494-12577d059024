document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const galleryItems = document.querySelectorAll('.gallery-item');
    const filterBtns = document.querySelectorAll('.filter-btn');
    const lightbox = document.querySelector('.lightbox');
    const lightboxImg = document.querySelector('.lightbox-img');
    const lightboxTitle = document.querySelector('.lightbox-title');
    const lightboxDesc = document.querySelector('.lightbox-desc');
    const closeLightbox = document.querySelector('.close-lightbox');
    const prevBtn = document.querySelector('.lightbox-prev');
    const nextBtn = document.querySelector('.lightbox-next');
    const lazyImages = document.querySelectorAll('.lazy-image');
    
    // Current active item in lightbox
    let currentIndex = 0;
    let visibleItems = [...galleryItems]; // Initially all items are visible
    let loadedImages = new Set(); // Track which images have been loaded
    
    // Check for missing images and handle errors
    function handleImageError(img) {
        console.warn('Image not found:', img.getAttribute('data-src'));
        // Optionally display a fallback image
        img.onerror = null; // Prevent infinite error loops
    }
    
    // Initialize Masonry-like layout effect
    function adjustItemsHeight() {
        // Reset previous inline heights
        galleryItems.forEach(item => {
            item.style.height = '';
        });
        
        // Let CSS grid handle the layout naturally
    }
    
    // Filter functionality
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Update active button
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const filterValue = this.getAttribute('data-filter');
            
            // Filter gallery items
            visibleItems = [];
            
            galleryItems.forEach(item => {
                const itemCategory = item.getAttribute('data-category');
                
                // First hide all with animation
                item.classList.add('hide');
                
                // Then show the matching ones
                if(filterValue === 'all' || filterValue === itemCategory) {
                    setTimeout(() => {
                        item.classList.remove('hide');
                        visibleItems.push(item);
                        
                        // Re-observe the lazy images for the newly visible items
                        const lazyImg = item.querySelector('.lazy-image');
                        if (lazyImg && !lazyImg.classList.contains('loaded') && 'IntersectionObserver' in window) {
                            // Create a new observer specifically for this image
                            const imageObserver = new IntersectionObserver((entries) => {
                                entries.forEach(entry => {
                                    if (entry.isIntersecting) {
                                        const img = entry.target;
                                        const src = img.getAttribute('data-src');
                                        
                                        if (!loadedImages.has(src)) {
                                            const tempImg = new Image();
                                            tempImg.onload = function() {
                                                img.src = src;
                                                img.classList.add('loaded');
                                                img.parentNode.classList.add('loaded');
                                                const placeholder = img.parentNode.querySelector('.img-placeholder');
                                                if (placeholder) {
                                                    placeholder.classList.add('fade-out');
                                                }
                                                loadedImages.add(src);
                                            };
                                            tempImg.src = src;
                                            imageObserver.unobserve(img);
                                        }
                                    }
                                });
                            }, {
                                rootMargin: '0px',
                                threshold: 0.1
                            });
                            
                            imageObserver.observe(lazyImg);
                        }
                    }, 300);
                }
            });
            
            // Re-adjust layout after filtering animation completes
            setTimeout(() => {
                adjustItemsHeight();
                cleanupUnusedImages();
            }, 400);
        });
    });
    
    // Lightbox functionality
    galleryItems.forEach((item, index) => {
        const viewBtn = item.querySelector('.view-btn');
        const overlay = item.querySelector('.item-overlay');

        // Click on view button
        viewBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            openLightbox(index);
        });

        // Click on overlay (anywhere on the image)
        overlay.addEventListener('click', function(e) {
            e.preventDefault();
            openLightbox(index);
        });

        // Add keyboard accessibility
        item.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                openLightbox(index);
            }
        });

        // Make gallery items focusable for keyboard navigation
        item.setAttribute('tabindex', '0');
        item.setAttribute('role', 'button');
        item.setAttribute('aria-label', `Visualizza immagine: ${item.querySelector('.meta-title').textContent}`);
    });
    
    function openLightbox(index) {
        currentIndex = index;
        const currentItem = galleryItems[index];
        const itemImg = currentItem.querySelector('.lazy-image');
        const itemTitle = currentItem.querySelector('.meta-title').textContent;
        const itemDesc = currentItem.querySelector('.meta-description').textContent;
        const itemCategory = currentItem.getAttribute('data-category');
        const itemDimensions = itemImg.getAttribute('data-dimensions') || '600x400';
        const loadingSpinner = document.querySelector('.loading-spinner');

        // Get the high-resolution image URL from data-src attribute
        const highResSrc = itemImg.getAttribute('data-src');

        // Show loading spinner while image loads
        loadingSpinner.classList.add('show');

        // Reset lightbox image
        lightboxImg.style.opacity = '0';
        lightboxImg.removeAttribute('src'); // Clear previous image

        // Set lightbox content
        lightboxTitle.textContent = itemTitle;
        lightboxDesc.textContent = itemDesc;

        // Set enhanced metadata
        const lightboxDimensions = document.querySelector('.lightbox-dimensions');
        const lightboxCategory = document.querySelector('.lightbox-category');
        const lightboxFormat = document.querySelector('.lightbox-format');

        if (lightboxDimensions) lightboxDimensions.textContent = itemDimensions;
        if (lightboxCategory) lightboxCategory.textContent = itemCategory.charAt(0).toUpperCase() + itemCategory.slice(1);
        if (lightboxFormat) lightboxFormat.textContent = 'JPG';
        
        // Show lightbox with animation
        lightbox.classList.add('show');
        
        // Create a new Image object to preload the high-res image
        const preloadImg = new Image();
        preloadImg.onload = function() {
            // Hide spinner once image is loaded
            loadingSpinner.classList.remove('show');
            
            // Set the lightbox image source and show it
            lightboxImg.src = highResSrc;
            lightboxImg.alt = itemTitle; // Set alt text for accessibility
            lightboxImg.style.opacity = '1';
            
            // If the original thumbnail image isn't loaded yet, update it too
            if (!itemImg.classList.contains('loaded')) {
                itemImg.src = highResSrc;
                itemImg.classList.add('loaded');
                const placeholder = itemImg.parentNode.querySelector('.img-placeholder');
                if (placeholder) {
                    placeholder.classList.add('fade-out');
                }
                loadedImages.add(highResSrc);
            }
        };
        
        preloadImg.onerror = function() {
            // Hide spinner if image fails to load
            loadingSpinner.classList.remove('show');
            console.warn('Failed to load lightbox image:', highResSrc);
            
            // You could set a fallback image or message here
            lightboxImg.src = itemImg.classList.contains('loaded') ? itemImg.src : highResSrc;
            lightboxImg.style.opacity = '1';
            
            // Maybe show an error message
            lightboxDesc.textContent = itemDesc + ' (Immagine non disponibile)';
        };
        
        // Start loading the image
        preloadImg.src = highResSrc;
        
        // Disable body scrolling when lightbox is open
        document.body.style.overflow = 'hidden';
    }
    
    function closeLightboxFunc() {
        lightbox.classList.remove('show');
        
        // Re-enable body scrolling
        document.body.style.overflow = '';
    }
    
    function navigateLightbox(direction) {
        // Find next visible item index
        let newIndex = currentIndex;
        
        do {
            newIndex = (newIndex + direction + visibleItems.length) % visibleItems.length;
            
            // If we've gone through all items and none are visible, break
            if(newIndex === currentIndex) break;
            
        } while(!visibleItems.includes(galleryItems[newIndex]));
        
        if(visibleItems.includes(galleryItems[newIndex])) {
            openLightbox(newIndex);
        }
    }
    
    // Event listeners for lightbox
    closeLightbox.addEventListener('click', closeLightboxFunc);
    
    lightbox.addEventListener('click', function(e) {
        if(e.target === lightbox) {
            closeLightboxFunc();
        }
    });
    
    prevBtn.addEventListener('click', function() {
        navigateLightbox(-1);
    });
    
    nextBtn.addEventListener('click', function() {
        navigateLightbox(1);
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if(!lightbox.classList.contains('show')) return;
        
        switch(e.key) {
            case 'Escape':
                closeLightboxFunc();
                break;
            case 'ArrowLeft':
                navigateLightbox(-1);
                break;
            case 'ArrowRight':
                navigateLightbox(1);
                break;
        }
    });
    
    // WordPress integration helper - for when the gallery is part of WordPress content
    function initializeWordPressGallery() {
        // If we're in WordPress, create a class to identify our gallery
        if(typeof wp !== 'undefined') {
            const container = document.querySelector('.gallery-container');
            if(container) {
                container.classList.add('wp-block-custom-gallery-block');
            }
        }
        
        // Re-init the gallery if it's added dynamically through WordPress
        document.addEventListener('DOMNodeInserted', function(e) {
            if(e.target.classList && e.target.classList.contains('gallery-container')) {
                // Reinitialize all events and functionality
                // This is just a basic check - you might need a more sophisticated approach
                adjustItemsHeight();
            }
        });
    }
    
    // Lazy loading implementation with Intersection Observer
    function setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        const src = img.getAttribute('data-src');
                        
                        if (!loadedImages.has(src)) {
                            // Create a new image to preload
                            const tempImg = new Image();
                            
                            tempImg.onload = function() {
                                // Once the full image is loaded
                                img.src = src;
                                img.classList.add('loaded');
                                img.parentNode.classList.add('loaded');

                                // Fade out the placeholder
                                const placeholder = img.parentNode.querySelector('.img-placeholder');
                                if (placeholder) {
                                    placeholder.classList.add('fade-out');
                                }

                                loadedImages.add(src);
                            };
                            
                            tempImg.onerror = function() {
                                console.warn('Failed to load image:', src);
                                // You could set a fallback image here if needed
                                // img.src = 'img/fallback.jpg';
                                
                                // Continue with removing the placeholder anyway
                                const placeholder = img.parentNode.querySelector('.img-placeholder');
                                if (placeholder) {
                                    placeholder.classList.add('fade-out');
                                }
                                
                                // Mark as processed even if it failed
                                loadedImages.add(src);
                            };
                            
                            tempImg.src = src;
                            
                            // Stop observing this image
                            observer.unobserve(img);
                        }
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.1
            });
            
            // Observe all lazy images
            lazyImages.forEach(img => {
                imageObserver.observe(img);
                
                // Also add error handling to the actual image element
                img.onerror = function() {
                    handleImageError(img);
                };
            });
        } else {
            // Fallback for browsers that don't support Intersection Observer
            lazyLoadImagesImmediately();
        }
    }
    
    // Fallback function to load all images immediately
    function lazyLoadImagesImmediately() {
        lazyImages.forEach(img => {
            const src = img.getAttribute('data-src');
            img.src = src;
            img.classList.add('loaded');
            img.parentNode.classList.add('loaded');

            const placeholder = img.parentNode.querySelector('.img-placeholder');
            if (placeholder) {
                placeholder.classList.add('fade-out');
            }

            loadedImages.add(src);
        });
    }

    // Memory management function to cleanup unused images when filtering
    function cleanupUnusedImages() {
        const currentlyVisibleSrcs = new Set();
        
        // Collect all image sources currently visible
        visibleItems.forEach(item => {
            const img = item.querySelector('.lazy-image');
            if (img) {
                currentlyVisibleSrcs.add(img.getAttribute('data-src'));
            }
        });
        
        // Optional: Release memory for images that are no longer visible
        // This depends on browser implementation and garbage collection
        // but we'll mark them for potential cleanup
        if (loadedImages.size > 100) { // Arbitrary threshold
            // Only perform cleanup if we have lots of images loaded
            loadedImages.forEach(src => {
                if (!currentlyVisibleSrcs.has(src)) {
                    loadedImages.delete(src);
                }
            });
        }
    }
    
    // Initialize gallery
    adjustItemsHeight();
    setupLazyLoading();
    initializeWordPressGallery();
    
    // Resize handler for responsive layout
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(adjustItemsHeight, 100);
    });
});