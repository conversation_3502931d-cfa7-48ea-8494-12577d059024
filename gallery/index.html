<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galleria Fotografica Elegante</title>
    <link rel="stylesheet" href="style.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&family=Raleway:wght@300;400;500;600&display=swap" rel="stylesheet">
    <!-- Intersection Observer Polyfill for older browsers -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=IntersectionObserver"></script>
</head>
<body>
    <div class="gallery-container">
        <div class="gallery-header">
            <h2>Galleria Fotografica</h2>
            <p>Una collezione curata di immagini straordinarie</p>
            
            <div class="gallery-filter">
                <button class="filter-btn active" data-filter="all">Tutti</button>
                <button class="filter-btn" data-filter="natura">Natura</button>
                <button class="filter-btn" data-filter="architettura">Architettura</button>
                <button class="filter-btn" data-filter="persone">Persone</button>
                <button class="filter-btn" data-filter="astratto">Astratto</button>
            </div>
        </div>
        
        <div class="gallery-grid">
            <!-- Gallery items -->
            <div class="gallery-item" data-category="natura">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0001.jpg')"></div>
                        <img class="lazy-image" data-src="img/0001.jpg" alt="Natura selvaggia">
                    </div>
                    <div class="item-overlay">
                        <h3>Natura Selvaggia</h3>
                        <p>Foresta incontaminata</p>
                        <button class="view-btn"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="architettura">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0002.jpg')"></div>
                        <img class="lazy-image" data-src="img/0002.jpg" alt="Architettura moderna">
                    </div>
                    <div class="item-overlay">
                        <h3>Architettura Moderna</h3>
                        <p>Linee e prospettive</p>
                        <button class="view-btn"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="persone">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0003.jpg')"></div>
                        <img class="lazy-image" data-src="img/0003.jpg" alt="Ritratto artistico">
                    </div>
                    <div class="item-overlay">
                        <h3>Espressioni</h3>
                        <p>Ritratto in bianco e nero</p>
                        <button class="view-btn"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="natura">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0004.jpg')"></div>
                        <img class="lazy-image" data-src="img/0004.jpg" alt="Oceano blu">
                    </div>
                    <div class="item-overlay">
                        <h3>Mare Infinito</h3>
                        <p>Onde e orizzonti</p>
                        <button class="view-btn"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="astratto">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0005.jpg')"></div>
                        <img class="lazy-image" data-src="img/0005.jpg" alt="Arte astratta">
                    </div>
                    <div class="item-overlay">
                        <h3>Visioni</h3>
                        <p>Arte concettuale</p>
                        <button class="view-btn"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="architettura">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0006.jpg')"></div>
                        <img class="lazy-image" data-src="img/0006.jpg" alt="Vita urbana">
                    </div>
                    <div class="item-overlay">
                        <h3>Metropoli</h3>
                        <p>Vita urbana</p>
                        <button class="view-btn"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="persone">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0007.jpg')"></div>
                        <img class="lazy-image" data-src="img/0007.jpg" alt="Stile moderno">
                    </div>
                    <div class="item-overlay">
                        <h3>Moda</h3>
                        <p>Stile contemporaneo</p>
                        <button class="view-btn"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="astratto">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0008.jpg')"></div>
                        <img class="lazy-image" data-src="img/0008.jpg" alt="Colori e forme">
                    </div>
                    <div class="item-overlay">
                        <h3>Cromie</h3>
                        <p>Esplosione di colori</p>
                        <button class="view-btn"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="natura">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0009.jpg')"></div>
                        <img class="lazy-image" data-src="img/0009.jpg" alt="Paesaggio montano">
                    </div>
                    <div class="item-overlay">
                        <h3>Altitudini</h3>
                        <p>Panorami montani</p>
                        <button class="view-btn"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lightbox -->
    <div class="lightbox">
        <div class="lightbox-content">
            <span class="close-lightbox">&times;</span>
            <div class="lightbox-img-container">
                <img src="" alt="" class="lightbox-img">
                <div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div>
            </div>
            <div class="lightbox-caption">
                <h3 class="lightbox-title"></h3>
                <p class="lightbox-desc"></p>
            </div>
            <button class="lightbox-prev"><i class="fas fa-chevron-left"></i></button>
            <button class="lightbox-next"><i class="fas fa-chevron-right"></i></button>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>