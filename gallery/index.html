<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galleria Fotografica Elegante</title>
    <link rel="stylesheet" href="style.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&family=Raleway:wght@300;400;500;600&display=swap" rel="stylesheet">
    <!-- Intersection Observer Polyfill for older browsers -->
    <script src="https://polyfill.io/v3/polyfill.min.js?features=IntersectionObserver"></script>
</head>
<body>
    <div class="gallery-container">
        <div class="gallery-header">
            <h2>Galleria Fotografica</h2>
            <p>Una collezione curata di immagini straordinarie</p>
            
            <div class="gallery-filter">
                <button class="filter-btn active" data-filter="all">Tutti</button>
                <button class="filter-btn" data-filter="natura">Natura</button>
                <button class="filter-btn" data-filter="architettura">Architettura</button>
                <button class="filter-btn" data-filter="persone">Persone</button>
                <button class="filter-btn" data-filter="astratto">Astratto</button>
            </div>
        </div>
        
        <div class="gallery-grid">
            <!-- Gallery items -->
            <div class="gallery-item" data-category="natura" data-image-id="0001">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0001.jpg')"></div>
                        <img class="lazy-image" data-src="img/0001.jpg" alt="Natura selvaggia" data-dimensions="600x400">
                    </div>
                    <div class="item-overlay">
                        <div class="item-metadata">
                            <h3 class="meta-title">Natura Selvaggia</h3>
                            <p class="meta-description">Foresta incontaminata con alberi secolari e vegetazione rigogliosa</p>
                            <div class="meta-details">600×400 • Natura • JPG</div>
                        </div>
                        <button class="view-btn" aria-label="Visualizza immagine a schermo intero"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="architettura" data-image-id="0002">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0002.jpg')"></div>
                        <img class="lazy-image" data-src="img/0002.jpg" alt="Architettura moderna" data-dimensions="600x400">
                    </div>
                    <div class="item-overlay">
                        <div class="item-metadata">
                            <h3 class="meta-title">Architettura Moderna</h3>
                            <p class="meta-description">Linee geometriche e prospettive innovative nell'architettura contemporanea</p>
                            <div class="meta-details">600×400 • Architettura • JPG</div>
                        </div>
                        <button class="view-btn" aria-label="Visualizza immagine a schermo intero"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="persone" data-image-id="0003">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0003.jpg')"></div>
                        <img class="lazy-image" data-src="img/0003.jpg" alt="Ritratto artistico" data-dimensions="600x400">
                    </div>
                    <div class="item-overlay">
                        <div class="item-metadata">
                            <h3 class="meta-title">Espressioni</h3>
                            <p class="meta-description">Ritratto artistico in bianco e nero che cattura l'essenza dell'emozione</p>
                            <div class="meta-details">600×400 • Persone • JPG</div>
                        </div>
                        <button class="view-btn" aria-label="Visualizza immagine a schermo intero"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>

            <div class="gallery-item" data-category="natura" data-image-id="0004">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0004.jpg')"></div>
                        <img class="lazy-image" data-src="img/0004.jpg" alt="Oceano blu" data-dimensions="600x400">
                    </div>
                    <div class="item-overlay">
                        <div class="item-metadata">
                            <h3 class="meta-title">Mare Infinito</h3>
                            <p class="meta-description">Onde cristalline e orizzonti senza fine in una giornata serena</p>
                            <div class="meta-details">600×400 • Natura • JPG</div>
                        </div>
                        <button class="view-btn" aria-label="Visualizza immagine a schermo intero"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>

            <div class="gallery-item" data-category="astratto" data-image-id="0005">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0005.jpg')"></div>
                        <img class="lazy-image" data-src="img/0005.jpg" alt="Arte astratta" data-dimensions="600x400">
                    </div>
                    <div class="item-overlay">
                        <div class="item-metadata">
                            <h3 class="meta-title">Visioni</h3>
                            <p class="meta-description">Arte concettuale che esplora forme e colori in composizioni uniche</p>
                            <div class="meta-details">600×400 • Astratto • JPG</div>
                        </div>
                        <button class="view-btn" aria-label="Visualizza immagine a schermo intero"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
            
            <div class="gallery-item" data-category="architettura" data-image-id="0006">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0006.jpg')"></div>
                        <img class="lazy-image" data-src="img/0006.jpg" alt="Vita urbana" data-dimensions="600x400">
                    </div>
                    <div class="item-overlay">
                        <div class="item-metadata">
                            <h3 class="meta-title">Metropoli</h3>
                            <p class="meta-description">Il dinamismo della vita urbana tra grattacieli e movimento</p>
                            <div class="meta-details">600×400 • Architettura • JPG</div>
                        </div>
                        <button class="view-btn" aria-label="Visualizza immagine a schermo intero"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>

            <div class="gallery-item" data-category="persone" data-image-id="0007">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0007.jpg')"></div>
                        <img class="lazy-image" data-src="img/0007.jpg" alt="Stile moderno" data-dimensions="600x400">
                    </div>
                    <div class="item-overlay">
                        <div class="item-metadata">
                            <h3 class="meta-title">Moda</h3>
                            <p class="meta-description">Stile contemporaneo e tendenze della moda moderna</p>
                            <div class="meta-details">600×400 • Persone • JPG</div>
                        </div>
                        <button class="view-btn" aria-label="Visualizza immagine a schermo intero"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>

            <div class="gallery-item" data-category="astratto" data-image-id="0008">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0008.jpg')"></div>
                        <img class="lazy-image" data-src="img/0008.jpg" alt="Colori e forme" data-dimensions="600x400">
                    </div>
                    <div class="item-overlay">
                        <div class="item-metadata">
                            <h3 class="meta-title">Cromie</h3>
                            <p class="meta-description">Esplosione di colori vivaci in composizioni astratte</p>
                            <div class="meta-details">600×400 • Astratto • JPG</div>
                        </div>
                        <button class="view-btn" aria-label="Visualizza immagine a schermo intero"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>

            <div class="gallery-item" data-category="natura" data-image-id="0009">
                <div class="gallery-item-inner">
                    <div class="img-container">
                        <div class="img-placeholder" style="background-image: url('img/t_0009.jpg')"></div>
                        <img class="lazy-image" data-src="img/0009.jpg" alt="Paesaggio montano" data-dimensions="600x400">
                    </div>
                    <div class="item-overlay">
                        <div class="item-metadata">
                            <h3 class="meta-title">Altitudini</h3>
                            <p class="meta-description">Panorami montani mozzafiato tra vette e vallate</p>
                            <div class="meta-details">600×400 • Natura • JPG</div>
                        </div>
                        <button class="view-btn" aria-label="Visualizza immagine a schermo intero"><i class="fas fa-search-plus"></i></button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Lightbox -->
    <div class="lightbox">
        <div class="lightbox-content">
            <span class="close-lightbox">&times;</span>
            <div class="lightbox-img-container">
                <img src="" alt="" class="lightbox-img">
                <div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i></div>
            </div>
            <div class="lightbox-caption">
                <h3 class="lightbox-title"></h3>
                <p class="lightbox-desc"></p>
                <div class="lightbox-metadata">
                    <span class="lightbox-dimensions"></span>
                    <span class="lightbox-category"></span>
                    <span class="lightbox-format"></span>
                </div>
            </div>
            <button class="lightbox-prev"><i class="fas fa-chevron-left"></i></button>
            <button class="lightbox-next"><i class="fas fa-chevron-right"></i></button>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>