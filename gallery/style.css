/* Reset and base styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9f9f9;
    padding: 0;
    margin: 0;
}

.gallery-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px 20px;
}

/* Gallery header styles */
.gallery-header {
    text-align: center;
    margin-bottom: 40px;
}

.gallery-header h2 {
    font-family: 'Raleway', sans-serif;
    font-size: 2.5rem;
    font-weight: 500;
    margin-bottom: 10px;
    color: #2d2d2d;
    letter-spacing: 1px;
}

.gallery-header p {
    font-size: 1.1rem;
    color: #777;
    max-width: 600px;
    margin: 0 auto 30px;
}

/* Filter buttons */
.gallery-filter {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.filter-btn {
    background: transparent;
    border: none;
    padding: 8px 20px;
    margin: 5px;
    font-size: 0.95rem;
    font-family: 'Poppins', sans-serif;
    color: #555;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border-radius: 30px;
}

.filter-btn::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: #222;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.filter-btn:hover {
    color: #111;
}

.filter-btn:hover::after {
    width: 70%;
}

.filter-btn.active {
    color: #111;
    font-weight: 500;
}

.filter-btn.active::after {
    width: 70%;
}

/* Gallery grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    grid-gap: 20px;
    grid-auto-flow: dense;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
    transition: transform 0.5s ease, opacity 0.5s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.gallery-item:focus {
    outline: none;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2), 0 0 0 3px rgba(74, 144, 226, 0.5);
    transform: translateY(-2px);
}

.gallery-item:focus .item-overlay {
    opacity: 1;
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.2) 0%,
        rgba(0, 0, 0, 0.4) 40%,
        rgba(0, 0, 0, 0.8) 100%
    );
}

/* Create some variety in the grid layout */
.gallery-item:nth-child(4n+1) {
    grid-row: span 1;
}

.gallery-item:nth-child(4n+2) {
    grid-row: span 2;
}

.gallery-item:nth-child(4n+3) {
    grid-column: span 1;
}

.gallery-item:nth-child(4n+4) {
    grid-column: span 1;
    grid-row: span 1;
}

.gallery-item.hide {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
    position: absolute;
}

.gallery-item-inner {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.img-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.img-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    filter: blur(10px);
    transform: scale(1.1);
    transition: opacity 0.5s ease;
    z-index: 1;
}

.img-placeholder.fade-out {
    opacity: 0;
}

.lazy-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform 0.8s ease, opacity 0.5s ease;
    opacity: 0;
    z-index: 2;
    position: relative;
}

.lazy-image.loaded {
    opacity: 1;
}

.item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.1) 40%,
        rgba(0, 0, 0, 0.7) 100%
    );
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 25px;
    opacity: 0;
    transition: all 0.4s ease;
    cursor: pointer;
    z-index: 10;
}

.gallery-item:hover .item-overlay {
    opacity: 1;
    background: linear-gradient(
        to bottom,
        rgba(0, 0, 0, 0.2) 0%,
        rgba(0, 0, 0, 0.4) 40%,
        rgba(0, 0, 0, 0.8) 100%
    );
}

.gallery-item:hover .lazy-image.loaded {
    transform: scale(1.05);
}

/* Loading state for images */
.gallery-item .img-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 5;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.gallery-item .img-container.loaded::before {
    opacity: 0;
}



.view-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    color: #222;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    transform: translateY(-20px) scale(0.8);
    opacity: 0;
    transition: all 0.4s ease;
    font-size: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 20;
}

.gallery-item:hover .view-btn {
    transform: translateY(0) scale(1);
    opacity: 1;
}

.view-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(0) scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.view-btn:active {
    transform: translateY(0) scale(0.95);
}

/* Enhanced overlay content */
.item-overlay h3 {
    font-family: 'Raleway', sans-serif;
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 8px;
    transform: translateY(20px);
    transition: transform 0.4s ease 0.1s;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.item-overlay p {
    font-size: 0.95rem;
    transform: translateY(20px);
    transition: transform 0.4s ease 0.2s;
    opacity: 0.9;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    margin-bottom: 0;
}

.gallery-item:hover .item-overlay h3,
.gallery-item:hover .item-overlay p {
    transform: translateY(0);
}

/* Tooltip for view button */
.view-btn::after {
    content: 'Clicca per ingrandire';
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 30;
}

.view-btn:hover::after {
    opacity: 1;
}

/* Lightbox styles */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.4s ease, visibility 0.4s ease;
}

.lightbox.show {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.lightbox-img-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.lightbox-img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    transform: scale(0.95);
    opacity: 0;
    transition: transform 0.5s ease, opacity 0.5s ease;
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 2rem;
    opacity: 0.8;
    z-index: 1;
    display: none;
}

.loading-spinner.show {
    display: block;
    animation: spin 1s infinite linear;
}

@keyframes spin {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

.lightbox.show .lightbox-img {
    transform: scale(1);
    opacity: 1;
}

.lightbox-caption {
    color: #fff;
    margin-top: 20px;
    text-align: center;
}

.lightbox-title {
    font-family: 'Raleway', sans-serif;
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.lightbox-desc {
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 15px;
}

.lightbox-metadata {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
    font-size: 0.9rem;
    opacity: 0.7;
}

.lightbox-metadata span {
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    font-size: 0.85rem;
}

.close-lightbox {
    position: absolute;
    top: -50px;
    right: 0;
    color: #fff;
    font-size: 2rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-lightbox:hover {
    color: #ddd;
}

.lightbox-prev,
.lightbox-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.3s ease;
}

.lightbox-prev {
    left: -60px;
}

.lightbox-next {
    right: -60px;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Animation for gallery items */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.gallery-item {
    animation: fadeIn 0.6s ease forwards;
    opacity: 0;
}

.gallery-item:nth-child(1) { animation-delay: 0.1s; }
.gallery-item:nth-child(2) { animation-delay: 0.2s; }
.gallery-item:nth-child(3) { animation-delay: 0.3s; }
.gallery-item:nth-child(4) { animation-delay: 0.4s; }
.gallery-item:nth-child(5) { animation-delay: 0.5s; }
.gallery-item:nth-child(6) { animation-delay: 0.6s; }
.gallery-item:nth-child(7) { animation-delay: 0.7s; }
.gallery-item:nth-child(8) { animation-delay: 0.8s; }
.gallery-item:nth-child(9) { animation-delay: 0.9s; }

/* WordPress Integration */
.wp-block-custom-gallery-block {
    width: 100%;
    margin: 2rem 0;
}

/* Responsive design */
@media screen and (max-width: 1024px) {
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media screen and (max-width: 768px) {
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .gallery-header h2 {
        font-size: 2rem;
    }
    
    .lightbox-prev {
        left: 10px;
    }
    
    .lightbox-next {
        right: 10px;
    }
}

@media screen and (max-width: 480px) {
    .gallery-grid {
        grid-template-columns: 1fr;
        grid-gap: 15px;
    }

    .gallery-item:nth-child(n) {
        grid-column: span 1;
        grid-row: span 1;
    }

    .gallery-header h2 {
        font-size: 1.8rem;
    }

    /* On mobile, show overlay by default for better UX */
    .item-overlay {
        opacity: 0.8;
        background: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.2) 40%,
            rgba(0, 0, 0, 0.8) 100%
        );
    }

    .view-btn {
        opacity: 1;
        transform: translateY(0) scale(1);
    }

    /* Hide tooltip on mobile */
    .view-btn::after {
        display: none;
    }

    .item-overlay h3,
    .item-overlay p {
        transform: translateY(0);
    }
}